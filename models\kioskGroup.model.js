const history = require("../models/plugins/history.plugin");

module.exports = (sequelize, DataTypes) => {
  const KioskGroup = sequelize.define(
    "KioskGroup",
    {
      kiosk_group_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(150),
        allowNull: false,
        unique: true,
      },
    },
    {
      tableName: "kiosk_group",
      timestamps: true,
      underscored: true,
    }
  );

  KioskGroup.associate = (models) => {
    KioskGroup.hasMany(models.KioskGroupSetting, { foreignKey: "kiosk_group_id", as: "settings" });
    // KioskGroup.hasMany(models.Device, { foreignKey: "kiosk_group_id", as: "devices" });
  };

  history(KioskGroup, sequelize, DataTypes);

  return KioskGroup;
};
