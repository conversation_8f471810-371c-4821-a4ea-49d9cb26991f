const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';
let authToken = '';

// Test credentials
const credentials = {
  email: "<EMAIL>",
  password: "Pa$$w0rd!"
};

// Helper function to make authenticated requests
const makeRequest = async (method, url, data = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
    };
    
    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }
    
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
};

// Test login and get token
const testLogin = async () => {
  console.log('\n🔐 ===== AUTHENTICATION TESTING =====');
  console.log('Testing login...');
  
  const result = await makeRequest('POST', '/auth/login', credentials);
  console.log(result)
  if (result.success) {
    authToken = result.data.data.tokens.access.token;
    console.log('✅ Login successful');
    console.log('📝 Response:', JSON.stringify(result.data, null, 2));
    return true;
  } else {
    console.log('❌ Login failed:', result.error);
    return false;
  }
};

// Test all Kiosk APIs
const testKioskAPIs = async () => {
  console.log('\n🏥 ===== KIOSK API TESTING =====');
  
  // 1. Test Device Settings API
  console.log('\n1️⃣ Testing GET /kiosk/device/{device_id}/setting');
  console.log('--- Valid device identifier ---');
  let result = await makeRequest('GET', '/kiosk/device/KIOSK_LOBBY_001/setting');
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  console.log('\n--- Invalid device identifier ---');
  result = await makeRequest('GET', '/kiosk/device/INVALID_DEVICE/setting');
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 2. Test Device Template API
  console.log('\n2️⃣ Testing GET /kiosk/device/{nda_template_id}/templates');
  console.log('--- Valid NDA template ID ---');
  // Get a valid template ID from previous response
  const deviceSettingResult = await makeRequest('GET', '/kiosk/device/KIOSK_LOBBY_001/setting');
  if (deviceSettingResult.success && deviceSettingResult.data.data.nda_template) {
    const templateId = deviceSettingResult.data.data.nda_template.nda_template_id;
    result = await makeRequest('GET', `/kiosk/device/${templateId}/templates`);
    console.log(`Status: ${result.status}`);
    console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  }
  
  console.log('\n--- Invalid template ID ---');
  result = await makeRequest('GET', '/kiosk/device/invalid-uuid/templates');
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 3. Test Fetch Guest by PIN API
  console.log('\n3️⃣ Testing POST /kiosk/guest/fetch');
  console.log('--- Valid request format ---');
  result = await makeRequest('POST', '/kiosk/guest/fetch', {
    pin: "1234",
    first_name: "John",
    last_name: "Doe"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  console.log('\n--- Invalid request (missing fields) ---');
  result = await makeRequest('POST', '/kiosk/guest/fetch', {
    pin: "1234"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 4. Test Patient Details API
  console.log('\n4️⃣ Testing POST /kiosk/patient/details');
  console.log('--- Valid request format ---');
  result = await makeRequest('POST', '/kiosk/patient/details', {
    appointment_guest_id: "123e4567-e89b-12d3-a456-426614174000",
    phone_number: "+**********",
    facility_id: "53c2ea2e-7664-4552-a3e8-419166d247b7"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 5. Test Outpatient Details API
  console.log('\n5️⃣ Testing POST /kiosk/outpatient/details');
  console.log('--- Valid request format ---');
  result = await makeRequest('POST', '/kiosk/outpatient/details', {
    cellphone_number: "+**********",
    birth_date: "1990-01-01"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 6. Test Add Guest API
  console.log('\n6️⃣ Testing POST /kiosk/guest/add');
  console.log('--- Valid guest creation ---');
  result = await makeRequest('POST', '/kiosk/guest/add', {
    first_name: "Test",
    last_name: "Guest",
    phone_number: "+**********",
    email: "<EMAIL>",
    facility_id: "53c2ea2e-7664-4552-a3e8-419166d247b7"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 7. Test Patient Guest Checkin API
  console.log('\n7️⃣ Testing POST /kiosk/checkin/patient-guest');
  console.log('--- Valid checkin request ---');
  result = await makeRequest('POST', '/kiosk/checkin/patient-guest', {
    patient_guest_id: "123e4567-e89b-12d3-a456-426614174000",
    facility_id: "53c2ea2e-7664-4552-a3e8-419166d247b7"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 8. Test Inpatient Details API
  console.log('\n8️⃣ Testing POST /kiosk/inpatient/details');
  console.log('--- Valid inpatient request ---');
  result = await makeRequest('POST', '/kiosk/inpatient/details', {
    patient_id: "123e4567-e89b-12d3-a456-426614174000",
    facility_id: "53c2ea2e-7664-4552-a3e8-419166d247b7"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
};

// Test all Device APIs
const testDeviceAPIs = async () => {
  console.log('\n📱 ===== DEVICE API TESTING =====');
  
  const facilityId = "53c2ea2e-7664-4552-a3e8-419166d247b7";
  
  // 1. Test Get All Devices
  console.log('\n1️⃣ Testing GET /facility/devices/{facilityId}');
  let result = await makeRequest('GET', `/facility/devices/${facilityId}`);
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 2. Test Get Device by ID
  console.log('\n2️⃣ Testing GET /facility/devices/{facilityId}/{deviceId}');
  // Get a device ID from the previous response
  if (result.success && result.data.data && result.data.data.length > 0) {
    const deviceId = result.data.data[0].device_id;
    result = await makeRequest('GET', `/facility/devices/${facilityId}/${deviceId}`);
    console.log(`Status: ${result.status}`);
    console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  } else {
    console.log('⚠️ No devices found to test individual device endpoint');
  }
  
  // 3. Test Create Device
  console.log('\n3️⃣ Testing POST /facility/devices/{facilityId}');
  console.log('--- Valid device creation ---');
  result = await makeRequest('POST', `/facility/devices/${facilityId}`, {
    name: "Comprehensive Test Kiosk",
    identifier: "KIOSK_COMP_TEST_001",
    kiosk_group_id: "7b417c80-d1ac-4ed3-ba02-b38ce4f7653d",
    facility_id: facilityId,
    building_id: "de1c2dae-2b39-4a74-aad0-7b356818a319",
    floor_id: "bb73e8bf-5741-4070-94cb-0aeef5cb0ab0",
    room_id: "be3733ea-0dc2-43a3-9aa0-3d14deaaaf16"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  let createdDeviceId = null;
  if (result.success) {
    createdDeviceId = result.data.data.device_id;
  }
  
  console.log('\n--- Invalid device creation (missing required fields) ---');
  result = await makeRequest('POST', `/facility/devices/${facilityId}`, {
    name: "",
    identifier: "INVALID_DEVICE"
  });
  console.log(`Status: ${result.status}`);
  console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  
  // 4. Test Update Device
  if (createdDeviceId) {
    console.log('\n4️⃣ Testing PATCH /facility/devices/{facilityId}/{deviceId}');
    console.log('--- Valid device update ---');
    result = await makeRequest('PATCH', `/facility/devices/${facilityId}/${createdDeviceId}`, {
      name: "Updated Comprehensive Test Kiosk"
    });
    console.log(`Status: ${result.status}`);
    console.log('Response:', JSON.stringify(result.success ? result.data : result.error, null, 2));
  } else {
    console.log('\n4️⃣ Skipping device update test - no device was created');
  }
};

// Main test runner
const runAllTests = async () => {
  console.log('🚀 ===== COMPREHENSIVE API TESTING STARTED =====');
  console.log('Testing ALL Kiosk and Device APIs...\n');
  
  const loginSuccess = await testLogin();
  
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }
  
  await testKioskAPIs();
  await testDeviceAPIs();
  
  console.log('\n✅ ===== COMPREHENSIVE API TESTING COMPLETED =====');
  console.log('All available endpoints have been tested!');
};

// Run the comprehensive tests
runAllTests().catch(console.error);
